<template>
  <div id="student-communication-records">
    <!-- 组件标题 -->
    <div class="component-header">
      <h3>学生进出通信记录</h3>
    </div>

    <!-- Tab 切换按钮 -->
    <div class="tab-container">
      <div class="tab-item" :class="{ active: activeTab === 'entry' }" @click="switchTab('entry')">
        <div class="tab-icon">📥</div>
        <div class="tab-text">进入记录</div>
        <div class="tab-count">({{ entryData.data.length }})</div>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'exit' }" @click="switchTab('exit')">
        <div class="tab-icon">📤</div>
        <div class="tab-text">离开记录</div>
        <div class="tab-count">({{ exitData.data.length }})</div>
      </div>
    </div>

    <!-- 滚动表格 -->
    <div class="board-content">
      <dv-scroll-board :config="config" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'StudentCommunicationRecords',
  data () {
    return {
      activeTab: 'entry', // 当前激活的tab，默认为进入记录
      // 进入记录数据
      entryData: {
        header: ['学生姓名', '学号', '二级学院', '进入时间', '位置信息', '通信状态'],
        data: [
          ['张明', '2021001', '电子信息工程学院', '08:30:15', '东门', '正常'],
          ['李华', '2021002', '机械电气工程学院', '08:45:22', '南门', '正常'],
          ['王芳', '2021003', '石油化工学院', '09:12:08', '东门', '正常'],
          ['刘强', '2021004', '旅游与商务经济管理学院', '09:25:33', '北门', '正常'],
          ['陈静', '2021005', '生物工程学院', '09:38:45', '东门', '正常'],
          ['赵伟', '2021006', '交通工程学院', '10:15:12', '南门', '正常'],
          ['孙丽', '2021007', '传媒学院', '10:28:56', '东门', '正常'],
          ['周杰', '2021008', '资源与环境学院', '10:45:33', '北门', '正常'],
          ['吴敏', '2021009', '纺织服装学院', '11:02:18', '南门', '正常'],
          ['郑浩', '2021010', '新媒体产业学院', '11:18:44', '东门', '正常']
        ]
      },
      // 离开记录数据
      exitData: {
        header: ['学生姓名', '学号', '二级学院', '离开时间', '位置信息', '通信状态'],
        data: [
          ['马超', '2020001', '电子信息工程学院', '17:30:25', '东门', '正常'],
          ['林雪', '2020002', '石油化工学院', '17:45:18', '南门', '正常'],
          ['黄磊', '2020003', '机械电气工程学院', '18:12:33', '东门', '正常'],
          ['许晴', '2020004', '传媒学院', '18:25:47', '北门', '正常'],
          ['邓涛', '2020005', '生物工程学院', '18:38:52', '东门', '正常'],
          ['谢娜', '2020006', '交通工程学院', '19:05:16', '南门', '正常'],
          ['袁华', '2020007', '旅游与商务经济管理学院', '19:18:29', '东门', '正常'],
          ['冯小刚', '2020008', '资源与环境学院', '19:32:41', '北门', '正常'],
          ['葛优', '2020009', '纺织服装学院', '19:45:55', '南门', '正常'],
          ['范冰冰', '2020010', '新媒体产业学院', '20:02:08', '东门', '正常']
        ]
      }
    }
  },
  computed: {
    config () {
      const baseConfig = {
        index: true,
        columnWidth: [100, 100, 160, 120, 80, 80],
        align: ['center', 'center', 'left', 'center', 'center', 'center'],
        rowNum: 4,
        headerBGC: 'rgb(33 50 95)', // 表头背景色
        headerHeight: 40,
        oddRowBGC: 'rgba(0, 44, 81, 0.8)',
        evenRowBGC: 'rgba(10, 29, 50, 0.8)',
        waitTime: 3000,
        carousel: 'single',
        hoverPause: true
      }

      if (this.activeTab === 'entry') {
        return {
          ...baseConfig,
          header: this.entryData.header,
          data: this.entryData.data
        }
      } else {
        return {
          ...baseConfig,
          header: this.exitData.header,
          data: this.exitData.data
        }
      }
    },
    // 今日进入人数
    todayEntryCount () {
      return this.entryData.data.length
    },
    // 今日离开人数
    todayExitCount () {
      return this.exitData.data.length
    },
    // 当前在校人数（简单计算：进入 - 离开）
    currentInSchool () {
      return Math.max(0, this.todayEntryCount - this.todayExitCount)
    }
  },
  methods: {
    switchTab (tab) {
      this.activeTab = tab
    },
    // 模拟实时数据更新
    updateData () {
      // 这里可以添加实时数据更新逻辑
      // 例如：从API获取最新的进出记录
    }
  },
  mounted () {
    // 组件挂载后可以开始数据更新
    this.updateData()
    // 可以设置定时器定期更新数据
    // this.dataUpdateTimer = setInterval(this.updateData, 30000) // 每30秒更新一次
  },
  beforeDestroy () {
    // 清理定时器
    if (this.dataUpdateTimer) {
      clearInterval(this.dataUpdateTimer)
    }
  }
}
</script>

<style lang="less">
#student-communication-records {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: rgba(6, 30, 93, 0.5);
  border: 1px solid rgba(1, 153, 209, 0.3); // 边框
  border-radius: 15px;
  padding: 15px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  // 组件标题
  .component-header {
    text-align: center;

    h3 {
      margin: 0 0 5px 0;
      color: #ffffff;
      font-size: 18px;
      font-weight: 600;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    }

    .header-subtitle {
      color: #b8c5d1;
      font-size: 12px;
      font-style: italic;
    }
  }

  // Tab切换容器
  .tab-container {
    display: flex;
    border-radius: 2px;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.2);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    max-width: 320px;
    margin-left: auto;
    margin-right: auto;

    .tab-item {
      flex: 1;
      padding: 0px 12px;
      text-align: center;
      color: #b8c5d1;
      background-color: rgba(25, 129, 246, 0.1);
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      position: relative;
      font-size: 12px;
      min-height: 30px;

      .tab-icon {
        font-size: 14px;
        opacity: 0.8;
      }

      .tab-text {
        font-size: 12px;
        font-weight: 400;
        white-space: nowrap;
      }

      .tab-count {
        font-size: 11px;
        color: #8fa4b3;
        margin-left: 2px;
      }

      &:hover {
        background-color: rgba(25, 129, 246, 0.2);
        color: #ffffff;

        .tab-icon {
          opacity: 1;
        }
      }

      &.active {
        background: linear-gradient(135deg, #1981f6 0%, #1565c0 100%);
        color: #ffffff;
        font-weight: 500;
        box-shadow: 0 2px 6px rgba(25, 129, 246, 0.3);

        .tab-icon {
          opacity: 1;
        }

        .tab-count {
          color: #ffffff;
          opacity: 0.9;
        }
      }

      &:not(:last-child) {
        border-right: 1px solid rgba(255, 255, 255, 0.08);
      }
    }
  }

  // 统计概览
  .stats-overview {
    display: flex;
    justify-content: space-around;
    margin-bottom: 15px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;

    .stat-item {
      text-align: center;

      .stat-label {
        font-size: 12px;
        color: #b8c5d1;
        margin-bottom: 5px;
      }

      .stat-value {
        font-size: 20px;
        font-weight: 600;
        color: #03d3ec;
        text-shadow: 0 0 10px rgba(3, 211, 236, 0.5);
      }
    }
  }

  // 表格内容区域
  .board-content {
    flex: 1;
    overflow: hidden;
    margin-top: 10px;
  }

  // 自定义滚动表格样式
  /deep/ .dv-scroll-board {
    .header {
      background: linear-gradient(135deg, #1981f6 0%, #1565c0 100%);
      box-shadow: 0 2px 8px rgba(25, 129, 246, 0.3);

      .header-item {
        color: #ffffff;
        font-weight: 600;
        font-size: 13px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }
    }

    .rows {
      .row-item {
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        &:hover {
          background-color: rgba(25, 129, 246, 0.2) !important;
          transform: translateX(3px);
        }

        .ceil {
          color: #ffffff;
          font-size: 12px;

          // 学生姓名列
          &:nth-child(2) {
            color: #64b5f6;
            font-weight: 500;
          }

          // 学号列
          &:nth-child(3) {
            color: #81c784;
            font-weight: 500;
          }

          // 时间列
          &:nth-child(5) {
            color: #ffb74d;
            font-weight: 500;
          }

          // 通信状态列
          &:last-child {
            color: #4caf50;
            font-weight: 600;
          }
        }
      }
    }
  }
}
</style>
